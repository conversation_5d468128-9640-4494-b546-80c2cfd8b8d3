use std::{error::Error, str::FromStr, sync::{Arc, LazyLock, RwLock}};
use alloy::signers::k256::sha2::digest::block_buffer::Lazy;
use log;

use crate::{config::ChainConfig, connector::ChainStatus, user::User, vira::{status::{pools::Pools, sync}, Vira}};

mod master;
mod config;
mod tools;
mod vira;
mod user;
mod errors;
mod connector;
mod strategy;
mod test;

// chain config
pub static CONFIG: LazyLock<ChainConfig> = LazyLock::new(|| {
    crate::config::x::new()
    //crate::config::pls::new()
});
// chain status
static STATUS: LazyLock<ChainStatus> = LazyLock::new(|| ChainStatus::new());
// use status
static USER : LazyLock<User> = LazyLock::new(|| User::new());

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    init().await?;
    Ok(())
}

async fn init() -> Result<(), Box<dyn Error>> {
    let mut vira = Vira::new().await;
    //初始化bot余额
    vira.contract.update_balance().await?;

    let connector = vira.connector.clone();
    // 调用init方法
    vira.sm.init(&vira.contract, connector.clone()).await?;
    sync::check_all_pools_mevs(&vira.contract, &vira.sm.pools).await?;
    vira.sm.save()?;

    // 创建一个通道用于通知trash组件同步完成
    let (sync_complete_tx, sync_complete_rx) = tokio::sync::mpsc::channel(1);
    
    // 在后台异步刷新所有资产的储备金
    let pools_clone = vira.sm.pools.clone();
    let connector_clone = connector.clone();
    tokio::spawn(async move {
        match sync::sync_all_reserves(pools_clone, connector_clone).await {
            Ok(()) => {
                println!("同步储备金完成");
                // 通知trash组件同步已完成
                let _ = sync_complete_tx.send(()).await;
                //auto drop sync_complete_tx
            }
            Err(e) => {
                eprintln!("同步储备金失败: {:?}", e);
            }
        }
    });
    
    let block_stream = vira.connector.subscribe_block().await.expect("订阅block失败");
    let logs_stream = vira.connector.subscribe_logs(vira.sm.pools.clone()).await.expect("订阅logs失败");

    let amm_stream = vira.connector.subscribe_block_and_logs(vira.sm.pools.clone()).await.expect("订阅失败");
    
    let vira = Arc::new(vira);
    let mut strategy = strategy::Strategy::new(vira.clone());

    //let pump_task = strategy.pump.listen(receipt_stream).await;
    strategy.trash.listen(amm_stream, sync_complete_rx).await?;

    Ok(())
}


#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_main() -> Result<(), Box<dyn Error>> {
        init().await?;
        Ok(())
    }
}