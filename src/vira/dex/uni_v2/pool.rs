use std::sync::Arc;

use alloy::{dyn_abi::abi::token, primitives::{Address, U256}, rpc::types::Log, sol, sol_types::SolEvent};
use serde::{Deserialize, Serialize};
use crate::{connector::Connector, impl_pool_display, vira::{consts::U_10000, errors::{DEXError, EventLogError, SwapSimulationError}, pool::{DexPool, PoolData, POOL}}};


sol! {
    /// Interface of the UniswapV2Pair
    #[derive(Debug, PartialEq, Eq)]
    #[sol(rpc)]
    contract IUniswapV2Pair {
        event Sync(uint112 reserve0, uint112 reserve1);
        function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
        function token0() external view returns (address);
        function token1() external view returns (address);
        function swap(uint256 amount0Out, uint256 amount1Out, address to, bytes calldata data);
    }
}


#[derive(<PERSON>bug, <PERSON><PERSON>, <PERSON><PERSON>ult, Serialize, Deserialize)]
pub struct UniV2Pool {
    pub data : PoolData,
}

// 使用宏实现Display特性
impl_pool_display!(UniV2Pool);

impl UniV2Pool {
}

//fee 9970/10000
pub fn get_amount_out(amount_in: U256, reserve_in: U256, reserve_out: U256, fee: U256) -> U256 {
    //dbg!(&amount_in, &reserve_in, &reserve_out, &fee);
    if amount_in.is_zero() || reserve_in.is_zero() || reserve_out.is_zero() {
        return U256::ZERO;
    }
    let amount_in_with_fee = amount_in * (U_10000 - fee); //default uni_v2 fee: 9970 (30/10000)
    let numerator = amount_in_with_fee * reserve_out;
    let denominator = reserve_in * U_10000 + amount_in_with_fee;

    numerator / denominator
}

//main pool && virtual pool
impl DexPool for UniV2Pool {
    fn data(&self) -> &PoolData {
        &self.data
    }

    fn data_mut(&mut self) -> &mut PoolData {
        &mut self.data
    }

    fn sync(&mut self, log: &Log) -> Result<bool, EventLogError> {
        let event_signature = log.topics()[0];

        if event_signature == IUniswapV2Pair::Sync::SIGNATURE_HASH {
            let sync_event = IUniswapV2Pair::Sync::decode_log(log.as_ref())?;

            let (reserve_0, reserve_1) = (
                sync_event.reserve0.to::<U256>(),
                sync_event.reserve1.to::<U256>(),
            );
            self.data.tokens[0].reserve = reserve_0;
            self.data.tokens[1].reserve = reserve_1;

            Ok(true)
        } else {
            Ok(false)
        }
    }

    fn update_reserve(&mut self, token_in: Address, _token_out: Address, amount_in: U256, amount_out: U256) {
        let data = self.data_mut();
        if token_in == data.tokens[0].addr {
            data.tokens[0].reserve += amount_in;
            data.tokens[1].reserve -= amount_out;
        } else {
            data.tokens[1].reserve += amount_in;
            data.tokens[0].reserve -= amount_out;
        }
    }

    fn simulate_swap(&self, base_token: Address, _quote_token: Address, amount_in: U256, custom_fee: Option<U256>) -> Result<U256, SwapSimulationError> {

        let data = self.data();
        let token_0 = &data.tokens[0];
        let token_1 = &data.tokens[1];

        if base_token == token_0.addr {
            let fee = custom_fee.unwrap_or(token_0.fee);
            Ok(get_amount_out(amount_in, token_0.reserve, token_1.reserve, data.fp + fee))
        } else {
            let fee = custom_fee.unwrap_or(token_1.fee);
            Ok(get_amount_out(amount_in, token_1.reserve, token_0.reserve, data.fp + fee))
        }
    }

    fn simulate_swap_by_index(&self, in_index : usize, out_index : usize, amount_in: U256, custom_fee: Option<U256>) -> Result<U256, SwapSimulationError> {
        let data = self.data();
        let token_in = &data.tokens[in_index];
        let token_out = &data.tokens[out_index];
        let fee = custom_fee.unwrap_or(token_in.fee);
        Ok(get_amount_out(amount_in, token_in.reserve, token_out.reserve, data.fp + fee))
    }

}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;

    fn create_test_pool(reserve0: U256, reserve1: U256, fee: U256) -> UniV2Pool {
        UniV2Pool {
            data: PoolData {
                addr: Address::from_str("0x1234567890123456789012345678901234567890").unwrap(),
                tokens: vec![
                    crate::vira::pool::PoolDataToken {
                        addr: Address::from_str("0x1000000000000000000000000000000000000000").unwrap(),
                        index: 0,
                        reserve: reserve0,
                        decimal: 18,
                        fee: U256::ZERO,
                        weight: U256::ZERO,
                        symbol: "TOKEN0".to_string(),
                    },
                    crate::vira::pool::PoolDataToken {
                        addr: Address::from_str("0x2000000000000000000000000000000000000000").unwrap(),
                        index: 1,
                        reserve: reserve1,
                        decimal: 18,
                        fee: U256::ZERO,
                        weight: U256::ZERO,
                        symbol: "TOKEN1".to_string(),
                    }
                ],
                ver: 2,
                fp: fee,
                ..Default::default()
            }
        }
    }

    #[test]
    fn test_get_amount_out() {
        // 测试基本场景
        let amount_in = U256::from(1000);
        let reserve_in = U256::from(1000000);
        let reserve_out = U256::from(1000000);
        let fee = U256::from(30); // 0.3% fee

        let amount_out = get_amount_out(amount_in, reserve_in, reserve_out, fee);
        dbg!(&amount_out);
        let k0 = reserve_in * reserve_out;
        let k1 = (amount_in + reserve_in) * (reserve_out - amount_out);
        dbg!(k0, k1);
        assert!(amount_out > U256::ZERO, "Should get non-zero output amount");
        assert!(k1 >= k0, "k: ERROR");

        // 测试边界情况
        assert_eq!(get_amount_out(U256::ZERO, reserve_in, reserve_out, fee), U256::ZERO, "Zero input should return zero");
        assert_eq!(get_amount_out(amount_in, U256::ZERO, reserve_out, fee), U256::ZERO, "Zero reserve_in should return zero");
        assert_eq!(get_amount_out(amount_in, reserve_in, U256::ZERO, fee), U256::ZERO, "Zero reserve_out should return zero");
    }

    #[test]
    fn test_pool_get_amount_out() {
        let decimal = U256::from(10).pow(U256::from(18));
        let pool = create_test_pool(
            U256::from(100000) * decimal, // 1M token0
            U256::from(1000000) * decimal, // 1M token1
            U256::from(30) // 0.3% fee
        );

        let token0 = pool.data.tokens[0].addr;
        let token1 = pool.data.tokens[1].addr;
        let amount_in = U256::from(1000) * decimal; // 1000 tokens

        // 测试正向交易 (token0 -> token1)
        let amount_out_0_to_1 = pool.simulate_swap(token0, token1, amount_in, None).unwrap();
        dbg!(&amount_out_0_to_1);
        assert!(amount_out_0_to_1 > U256::ZERO, "Should get non-zero output amount for 0->1");

        // 测试反向交易 (token1 -> token0)
        let amount_out_1_to_0 = pool.simulate_swap(token1, token0, amount_in, None).unwrap();
        dbg!(&amount_out_1_to_0);
        assert!(amount_out_1_to_0 > U256::ZERO, "Should get non-zero output amount for 1->0");

        // 测试自定义手续费
        let custom_fee = Some(U256::from(50)); // 0.5% fee
        let amount_out_custom_fee = pool.simulate_swap(token0, token1, amount_in, custom_fee).unwrap();
        dbg!(&amount_out_custom_fee);
        assert!(amount_out_custom_fee < amount_out_0_to_1, "Higher fee should result in lower output");
    }

    #[test]
    fn test_pool_get_amount_out_by_index() {
        let pool = create_test_pool(
            U256::from(1000000) * U256::from(10).pow(U256::from(18)), // 1M token0
            U256::from(1000000) * U256::from(10).pow(U256::from(18)), // 1M token1
            U256::from(30) // 0.3% fee
        );

        let amount_in = U256::from(1000) * U256::from(10).pow(U256::from(18)); // 1000 tokens

        // 测试正向交易 (index 0 -> 1)
        let amount_out_0_to_1 = pool.simulate_swap_by_index(0, 1, amount_in, None).unwrap();
        assert!(amount_out_0_to_1 > U256::ZERO, "Should get non-zero output amount for 0->1");

        // 测试反向交易 (index 1 -> 0)
        let amount_out_1_to_0 = pool.simulate_swap_by_index(1, 0, amount_in, None).unwrap();
        assert!(amount_out_1_to_0 > U256::ZERO, "Should get non-zero output amount for 1->0");

        // 测试自定义手续费
        let custom_fee = Some(U256::from(9950)); // 0.5% fee
        let amount_out_custom_fee = pool.simulate_swap_by_index(0, 1, amount_in, custom_fee).unwrap();
        assert!(amount_out_custom_fee < amount_out_0_to_1, "Higher fee should result in lower output");
    }
}


