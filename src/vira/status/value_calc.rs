use std::borrow::<PERSON><PERSON>;
use std::sync::Arc;
use alloy::primitives::{Address, U256};
use dashmap::DashMap;

use crate::vira::status::pools::Pools;
use crate::vira::{consts::U_2, pool::DexPool};
use crate::vira::pool::POOL;
use super::{PoolIndex, cache::Cache};
use colored::Colorize;

/// 价值计算器
/// 计算路径和池子的价值
pub struct ValueCalc;

impl ValueCalc {

    /// 获取池子索引的储备量
    pub fn get_reserves_by_index(
        all_pools: &Arc<Pools>,
        flow: &PoolIndex,
        cache: Option<&Cache>
    ) -> (U256, U256) {
        let (r_in, r_out) = if let Some(cache) = cache {
            if let Some(p) = cache.cache_data.get(&flow.addr) {
                p.reserve_by_index(flow.in_index, flow.out_index)
            } else {
                all_pools.data.get(&flow.addr).unwrap().reserve_by_index(flow.in_index, flow.out_index)
            }
        } else {
            all_pools.data.get(&flow.addr).unwrap().reserve_by_index(flow.in_index, flow.out_index)
        };
        (r_in, r_out)
    }

    /// 计算最低价值 -> (每一跳的价值序列, 路径中的最低价值, 最小值是否在排除池)
    ///
    /// 参数说明：
    /// - `pools_flow`: 池子流向序列
    /// - `all_pools`: 所有池子的引用
    /// - `cache`: 缓存数据（可选）
    /// - `is_exclude_main_pool`: 是否在计算最小值时排除主池
    ///   - true: 排除 `cache.exclude_pool` 对应的池子，强制 `is_min_value_at_exclude_pool` 为 false
    ///   - false: 保持现有逻辑，使用所有池子计算最小值
    ///
    /// 优化特点：
    /// - 简化变量使用，减少重复的最小值查找逻辑
    /// - 统一最小值计算函数，避免代码重复
    /// - 保持原有功能完整性和性能
    pub fn calc_lowest_value<T>(
        pools_flow: &[T],
        all_pools: &Arc<Pools>,
        cache: Option<&Cache>,
        is_exclude_main_pool: bool
    ) -> (Vec<U256>, U256, bool)
    where
        T: Borrow<PoolIndex> + std::fmt::Debug,
    {
        // 1) 收集储备数据并进行合法性校验
        let reserves: Vec<(U256, U256)> = pools_flow.iter()
            .map(|flow| {
                let flow: &PoolIndex = flow.borrow();
                let (r_in, r_out) = Self::get_reserves_by_index(all_pools, flow, cache);
                if r_out.is_zero() {
                    eprintln!("pools_flow: {:?}", flow);
                    eprintln!("r_in: {:?}, r_out: {:?}", r_in, r_out);
                    panic!("r_out is zero");
                }
                (r_in, r_out)
            })
            .collect();

        // 2) 计算每跳的价值
        let values = Self::calc_values(reserves);

        // 3) 计算最小值，根据参数决定是否排除主池
        let (lowest_value, is_min_value_at_exclude_pool) = if is_exclude_main_pool && cache.is_some() {
            // 排除主池模式：强制返回 false，只从非主池中找最小值
            let exclude_addr = cache.unwrap().exclude_pool;
            let filtered_lowest = values.iter()
                .enumerate()
                .filter(|(i, _)| {
                    // 过滤掉主池对应的值
                    let flow: &PoolIndex = pools_flow[*i].borrow();
                    flow.addr != exclude_addr
                })
                .map(|(_, &value)| value)
                .min()
                .unwrap_or_else(|| {
                    // 如果所有池都被排除，回退到全局最小值
                    *values.iter().min().unwrap_or(&U256::ZERO)
                });
            (filtered_lowest, false)
        } else {
            // 标准模式：找到最小值并判断是否在主池
            let (min_value, min_index) = values.iter()
                .enumerate()
                .min_by_key(|&(_, value)| value)
                .map(|(i, value)| (*value, i))
                .unwrap_or((U256::ZERO, 0));

            // 判断最小值是否在排除池
            let is_at_exclude_pool = cache
                .map(|cache| {
                    let flow: &PoolIndex = pools_flow[min_index].borrow();
                    flow.addr == cache.exclude_pool
                })
                .unwrap_or(false);

            (min_value, is_at_exclude_pool)
        };

        (values, lowest_value, is_min_value_at_exclude_pool)
    }


    /// 按路径顺序计算每一跳的价值，并将结果乘以 2（与原实现保持一致）
    /// 输入为各跳的储备对 (r_in, r_out)
    /// 计算规则：
    /// - 第 0 跳：value = r_in
    /// - 第 i 跳 (i>0)：若上一步的 r_out==0，则该跳 value=0；否则 value = 上一步 value * r_in / 上一步 r_out
    /// - 最终返回的每个 value 都会乘以常量 U_2
    pub fn calc_values(reserves: Vec<(U256, U256)>) -> Vec<U256> {
        let mut values_doubled: Vec<U256> = Vec::with_capacity(reserves.len());
        if reserves.is_empty() {
            return values_doubled;
        }

        let mut pre_val: U256 = U256::ZERO;
        let mut pre_out_r: U256 = U256::ZERO;

        for (index, (r_in, r_out)) in reserves.into_iter().enumerate() {
            let val = if index == 0 {
                r_in
            } else if pre_out_r.is_zero() {
                // 与旧逻辑保持一致：若上一跳的 r_out 为 0，当前价值记为 0，而非除零
                U256::ZERO
            } else {
                pre_val * r_in / pre_out_r
            };

            values_doubled.push(val * U_2);

            pre_val = val;
            pre_out_r = r_out;
        }

        values_doubled
    }

    /// 计算池子的价值
    pub fn calc_value_of_pool(_pre : Option<PoolIndex>, _pool : POOL, _pools : Arc<Pools>) -> (Address, U256) {
        (Address::ZERO, U256::ZERO)
    }

}