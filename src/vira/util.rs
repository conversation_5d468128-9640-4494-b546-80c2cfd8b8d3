use std::fmt::Display;

//所有一对的组合
pub fn all_sets<T>(arr : &Vec<T>) -> Vec<Vec<T>>
    where T : Clone
{
    let mut result = vec![];
    let len = arr.len();
    for i in 0..len {
        for j in i+1..len {
            result.push(vec![arr[i].clone(), arr[j].clone()]);
        }
    }  
    result
}

pub fn to_percent<T: Into<f64>>(numerator: T, denominator: T) -> f64 {
    let a_f64: f64 = numerator.into();
    let b_f64: f64 = denominator.into();
    a_f64 / b_f64 * 100.0
}

pub fn display_vec<T: Display >(vec: &Vec<T>) {
    for v in vec {
        println!("{}", v);
    }
}

pub async fn delay(ms: u64) {
    if ms > 0 {
        tokio::time::sleep(tokio::time::Duration::from_millis(ms)).await;
    }
}

#[cfg(test)]
mod tests {
    pub use super::*;

    #[test]
    pub fn test_all_sets(){
        let arr = vec![1,2,3];
        let result = all_sets(&arr);
        println!("result: {:?}", result);
    }
}